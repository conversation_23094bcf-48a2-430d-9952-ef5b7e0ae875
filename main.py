import asyncio
import os
from playwright.async_api import async_playwright

async def main():
    async with async_playwright() as p:
        # Get the absolute path to the Chrome executable
        chrome_path = os.path.abspath("fingerprint-chromium/Chromium/Application/chrome.exe")

        # Launch browser with proxy and custom Chrome executable
        browser = await p.chromium.launch(
            executable_path=chrome_path,

            headless=False,  # Set to True if you want headless mode
            args=[
                "--disable-blink-features=AutomationControlled",
                "--disable-dev-shm-usage",
                "--no-sandbox",
                "--disable-setuid-sandbox"
            ]
        )

        # Create a new page
        page = await browser.new_page()

        # Navigate to the website
        print("Opening https://www.genspark.ai...")
        await page.goto("https://www.genspark.ai")

        # Wait for 9999 seconds
        print("Waiting for 9999 seconds...")
        await asyncio.sleep(9999)

        # Close the browser
        await browser.close()

if __name__ == "__main__":
    asyncio.run(main())