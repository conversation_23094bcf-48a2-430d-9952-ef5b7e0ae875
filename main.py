import asyncio
import os
from playwright.async_api import async_playwright

async def main():
    async with async_playwright() as p:
        # Get the absolute path to the Chrome executable
        chrome_path = os.path.abspath("fingerprint-chromium/Chromium/Application/chrome.exe")

        # Launch browser with proxy and custom Chrome executable
        browser = await p.chromium.launch(
            executable_path=chrome_path,
            proxy={
                "server": "http://gw.dataimpulse.com:823"
            },
            headless=False,  # Set to True if you want headless mode
            args=[
                "--disable-blink-features=AutomationControlled",
                "--disable-dev-shm-usage",
                "--no-sandbox",
                "--disable-setuid-sandbox"
            ]
        )

        # Create a new page
        page = await browser.new_page()

        # Set up console listener to capture reCAPTCHA token
        def handle_console(msg):
            if msg.type == "log":
                print(f"Console output: {msg.text}")

        page.on("console", handle_console)

        # Navigate to the website
        print("Opening https://www.genspark.ai...")
        await page.goto("https://www.genspark.ai")

        # Wait a bit for the page to load
        await asyncio.sleep(5)

        # Execute reCAPTCHA using CDP
        print("Executing reCAPTCHA...")
        try:
            # Use CDP to execute the reCAPTCHA code
            await page.evaluate("""
                grecaptcha.ready(function() {
                    grecaptcha.execute('6Leq7KYqAAAAAGdd1NaUBJF9dHTPAKP7DcnaRc66', {action: 'submit'}).then(function(token) {
                        console.log('reCAPTCHA Token:', token);
                    });
                });
            """)
            print("reCAPTCHA execution initiated...")
        except Exception as e:
            print(f"Error executing reCAPTCHA: {e}")

        # Wait for 9999 seconds
        print("Waiting for 9999 seconds...")
        await asyncio.sleep(9999)

        # Close the browser
        await browser.close()

if __name__ == "__main__":
    asyncio.run(main())